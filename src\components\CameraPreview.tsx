import { forwardRef, useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { X, Camera, Video } from "lucide-react";
import { Language } from "@/utils/translations";

interface CameraPreviewProps {
  isCameraActive: boolean;
  countdown: number;
  language: Language;
  onCapturePhoto: () => void;
  onStopCamera: () => void;
  photosLength: number;
}

const CameraPreview = forwardRef<HTMLVideoElement, CameraPreviewProps>(
  ({ isCameraActive, countdown, language, onCapturePhoto, onStopCamera, photosLength }, ref) => {
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
      if (ref && typeof ref === 'object' && ref.current) {
        const video = ref.current;

        const handleLoadedMetadata = () => {
          setIsLoading(false);
          video.play().catch(console.error);
        };

        const handleCanPlay = () => setIsLoading(false);
        const handleLoadStart = () => setIsLoading(true);

        video.addEventListener('loadedmetadata', handleLoadedMetadata);
        video.addEventListener('canplay', handleCanPlay);
        video.addEventListener('loadstart', handleLoadStart);

        if (video.readyState >= 2) {
          setIsLoading(false);
        }

        return () => {
          video.removeEventListener('loadedmetadata', handleLoadedMetadata);
          video.removeEventListener('canplay', handleCanPlay);
          video.removeEventListener('loadstart', handleLoadStart);
        };
      }
    }, [ref, isCameraActive]);

    useEffect(() => {
      if (!isCameraActive) return;

      const startCamera = async () => {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({ video: true });
          if (ref && typeof ref === 'object' && ref.current) {
            ref.current.srcObject = stream;
          }
        } catch (err) {
          console.error('Error accessing camera:', err);
        }
      };

      startCamera();

      return () => {
        if (ref && typeof ref === 'object' && ref.current?.srcObject) {
          (ref.current.srcObject as MediaStream).getTracks().forEach((track) => track.stop());
        }
      };
    }, [ref, isCameraActive]);

    useEffect(() => {
      if (isCameraActive) {
        setIsLoading(true);
      }
    }, [isCameraActive]);

    if (!isCameraActive) return null;

    return (
      <div className="relative space-y-4">
        <div className="relative w-full h-64 rounded-xl border-2 border-primary shadow-elegant overflow-hidden bg-muted">
          <video
            ref={ref}
            autoPlay
            playsInline
            muted
            className="w-full h-full object-cover"
            style={{ opacity: isLoading ? 0 : 1, transition: 'opacity 0.3s ease' }}
          />
        </div>

        {countdown > 0 && (
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center rounded-xl">
            <div className="text-center">
              <div className="text-6xl font-bold text-white mb-2 animate-pulse">
                {countdown}
              </div>
              <p className="text-white text-lg">
                {language === 'ar' ? "جاري التقاط الصورة..." : "Capture en cours..."}
              </p>
            </div>
          </div>
        )}

        <div className="absolute top-4 right-4 flex gap-2">
          <Button
            type="button"
            variant="destructive"
            size="sm"
            onClick={onStopCamera}
            className="rounded-full"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {countdown === 0 && (
          <div className="flex gap-4 justify-center">
            <Button
              type="button"
              onClick={onCapturePhoto}
              className="flex items-center space-x-2 bg-primary hover:bg-primary/90"
              disabled={photosLength >= 5}
            >
              <Camera className="w-4 h-4" />
              <span>{language === 'ar' ? 'التقط صورة' : 'Prendre photo'}</span>
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={onStopCamera}
            >
              {language === 'ar' ? 'إلغاء' : 'Annuler'}
            </Button>
          </div>
        )}
      </div>
    );
  }
);

CameraPreview.displayName = "CameraPreview";

export default CameraPreview;
