@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern Design System for Agadir CivicHub 
   Inspired by Moroccan architecture and Mediterranean aesthetics
   All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Core Color Palette - Inspired by Agadir's architecture and ocean */
    --background: 0 0% 100%;
    --foreground: 220 13% 9%;

    --card: 0 0% 100%;
    --card-foreground: 220 13% 9%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 13% 9%;

    /* Primary: Deep Morocco Blue */
    --primary: 217 91% 35%;
    --primary-foreground: 0 0% 98%;
    --primary-glow: 217 91% 50%;

    /* Secondary: Warm Terracotta */
    --secondary: 14 82% 94%;
    --secondary-foreground: 14 82% 25%;

    /* Muted: Soft Sand */
    --muted: 47 36% 95%;
    --muted-foreground: 47 36% 35%;

    /* Accent: Argan Gold */
    --accent: 45 93% 47%;
    --accent-foreground: 45 15% 15%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 217 91% 35%;

    --radius: 0.75rem;

    /* Modern Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(217 91% 35%), hsl(217 91% 50%));
    --gradient-secondary: linear-gradient(135deg, hsl(14 82% 85%), hsl(14 82% 70%));
    --gradient-hero: linear-gradient(135deg, hsl(217 91% 35%) 0%, hsl(14 82% 55%) 100%);
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%) 0%, hsl(220 13% 97%) 100%);
    
    /* Shadows */
    --shadow-elegant: 0 10px 30px -10px hsl(217 91% 35% / 0.2);
    --shadow-glow: 0 0 40px hsl(217 91% 50% / 0.15);
    --shadow-card: 0 4px 20px -4px hsl(220 13% 9% / 0.1);

    /* Sidebar */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 217 91% 35%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 47 36% 95%;
    --sidebar-accent-foreground: 47 36% 25%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217 91% 35%;
  }

  .dark {
    --background: 220 27% 5%;
    --foreground: 210 40% 98%;

    --card: 220 27% 8%;
    --card-foreground: 210 40% 98%;

    --popover: 220 27% 8%;
    --popover-foreground: 210 40% 98%;

    --primary: 217 91% 50%;
    --primary-foreground: 220 27% 5%;
    --primary-glow: 217 91% 60%;

    --secondary: 14 82% 15%;
    --secondary-foreground: 14 82% 85%;

    --muted: 220 27% 12%;
    --muted-foreground: 220 13% 65%;

    --accent: 45 93% 40%;
    --accent-foreground: 220 27% 5%;

    --destructive: 0 62% 50%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 27% 15%;
    --input: 220 27% 15%;
    --ring: 217 91% 50%;

    /* Dark mode gradients */
    --gradient-primary: linear-gradient(135deg, hsl(217 91% 45%), hsl(217 91% 60%));
    --gradient-secondary: linear-gradient(135deg, hsl(14 82% 20%), hsl(14 82% 35%));
    --gradient-hero: linear-gradient(135deg, hsl(217 91% 40%) 0%, hsl(14 82% 40%) 100%);
    --gradient-card: linear-gradient(145deg, hsl(220 27% 8%) 0%, hsl(220 27% 12%) 100%);
    
    --shadow-elegant: 0 10px 30px -10px hsl(217 91% 50% / 0.3);
    --shadow-glow: 0 0 40px hsl(217 91% 60% / 0.2);
    --shadow-card: 0 4px 20px -4px hsl(220 27% 5% / 0.3);

    --sidebar-background: 220 27% 8%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 217 91% 50%;
    --sidebar-primary-foreground: 220 27% 5%;
    --sidebar-accent: 220 27% 12%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 220 27% 15%;
    --sidebar-ring: 217 91% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    @apply font-semibold tracking-tight;
  }
}

@layer utilities {
  /* Advanced Animation System */
  @keyframes fadeInUp {
    0% {
      opacity: 0;
      transform: translateY(20px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes slideInRight {
    0% {
      opacity: 0;
      transform: translateX(30px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  @keyframes scaleIn {
    0% {
      opacity: 0;
      transform: scale(0.95);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  @keyframes float {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-5px);
    }
  }
  
  @keyframes glow {
    0%, 100% {
      box-shadow: 0 0 20px hsl(217 91% 50% / 0.1);
    }
    50% {
      box-shadow: 0 0 30px hsl(217 91% 50% / 0.3);
    }
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }
  
  .animate-slide-in-right {
    animation: slideInRight 0.5s ease-out;
  }
  
  .animate-scale-in {
    animation: scaleIn 0.4s ease-out;
  }
  
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  
  .animate-glow {
    animation: glow 2s ease-in-out infinite;
  }

  /* Modern Card Effects */
  .glass-card {
    background: linear-gradient(145deg, 
      hsl(var(--card) / 0.8), 
      hsl(var(--card) / 0.4)
    );
    backdrop-filter: blur(10px);
    border: 1px solid hsl(var(--border) / 0.5);
  }
  
  .gradient-card {
    background: var(--gradient-card);
    box-shadow: var(--shadow-card);
  }
  
  .hero-gradient {
    background: var(--gradient-hero);
  }
  
  .primary-gradient {
    background: var(--gradient-primary);
  }
  
  .hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-elegant);
  }
  
  .interactive-glow {
    transition: all 0.3s ease;
  }
  
  .interactive-glow:hover {
    box-shadow: var(--shadow-glow);
  }

  /* Moroccan-inspired patterns (subtle) */
  .pattern-dots {
    background-image: radial-gradient(circle, hsl(var(--primary) / 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }
}