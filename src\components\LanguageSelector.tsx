import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
const agadirLogo = "/lovable-uploads/6d8cf473-65da-4cab-ad15-2e2b3cad7348.png";

interface LanguageSelectorProps {
  onLanguageSelect: (language: 'ar' | 'fr') => void;
}

const LanguageSelector = ({ onLanguageSelect }: LanguageSelectorProps) => {
  const [showSplash, setShowSplash] = useState(true);

  useEffect(() => {
    // Show splash screen for 3 seconds
    const timer = setTimeout(() => {
      setShowSplash(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  if (showSplash) {
    return (
      <div className="min-h-screen bg-gradient-hero flex flex-col items-center justify-center p-4 text-white overflow-hidden">
        <div className="absolute inset-0 pattern-dots opacity-20" />
        <div className="relative text-center space-y-8 animate-fade-in-up">
          {/* Modern Language Toggle */}
          <div className="flex items-center justify-center space-x-4 mb-16 animate-scale-in" style={{ animationDelay: '0.2s' }}>
            <span className="text-lg font-medium text-white/90">Français</span>
            <div className="w-14 h-7 bg-white/20 backdrop-blur-sm rounded-full flex items-center border border-white/30">
              <div className="w-5 h-5 bg-white rounded-full ml-1 shadow-sm"></div>
            </div>
            <span className="text-lg font-medium text-white/90">العربية</span>
          </div>

          {/* City Logo with Modern Effect */}
          <div className="flex justify-center mb-8 animate-float" style={{ animationDelay: '0.4s' }}>
            <div className="relative">
              <div className="absolute inset-0 bg-white/10 rounded-full blur-xl" />
              <img 
                src={agadirLogo} 
                alt="Agadir City Logo" 
                className="relative w-48 h-48 object-contain filter brightness-0 invert drop-shadow-2xl"
              />
            </div>
          </div>

          {/* City Names with Elegant Typography */}
          <div className="space-y-3 animate-slide-in-right" style={{ animationDelay: '0.6s' }}>
            <div className="text-lg font-medium tracking-wider text-white/80 font-serif">ΣΨΟΣ I 8ΧοΛΣΟ</div>
            <div className="text-3xl font-bold text-white font-serif">مدينة أكادير</div>
            <div className="text-xl font-semibold text-white/90 tracking-wide">VILLE D'AGADIR</div>
          </div>

          {/* Modern Call to Action */}
          <div className="mt-16 flex items-center justify-center animate-fade-in-up" style={{ animationDelay: '0.8s' }}>
            <div className="bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 border border-white/20">
              <span className="text-lg font-medium">إسحب للبدء</span>
              <svg className="w-6 h-6 ml-2 inline animate-float" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-hero flex items-center justify-center p-4 overflow-hidden">
      <div className="absolute inset-0 pattern-dots opacity-10" />
      <Card className="relative w-full max-w-md shadow-elegant border-0 glass-card animate-scale-in">
        <CardHeader className="text-center pb-6">
          <div className="w-20 h-20 mx-auto mb-6 animate-float">
            <div className="relative">
              <div className="absolute inset-0 bg-primary/20 rounded-full blur-lg" />
              <img 
                src={agadirLogo} 
                alt="Agadir City Logo" 
                className="relative w-full h-full object-contain drop-shadow-lg"
              />
            </div>
          </div>
          <CardTitle className="text-3xl font-serif text-primary">
            مدينة أكادير
          </CardTitle>
          <div className="text-lg text-primary/80 font-medium">
            VILLE D'AGADIR
          </div>
          <p className="text-muted-foreground mt-6 text-lg">
            اختر لغتك / Choisissez votre langue
          </p>
        </CardHeader>
        <CardContent className="space-y-4 px-6 pb-6">
          <Button 
            onClick={() => onLanguageSelect('ar')} 
            className="w-full h-16 text-lg bg-gradient-primary text-white border-0 hover-lift interactive-glow"
          >
            <span className="text-2xl font-medium">العربية</span>
          </Button>
          <Button 
            onClick={() => onLanguageSelect('fr')} 
            variant="outline" 
            className="w-full h-16 text-lg border-primary/20 hover:bg-primary/5 hover:border-primary/40 hover-lift"
          >
            <span className="text-xl font-medium text-primary">Français</span>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default LanguageSelector;