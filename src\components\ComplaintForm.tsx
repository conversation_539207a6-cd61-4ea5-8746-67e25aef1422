import { useState, useRef, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { AlertTriangle, Camera, Video, Mic, Send, CheckCircle, StopCircle, Play, Pause, X, Upload, ImageIcon, Zap } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { translations, Language } from "@/utils/translations";
import CameraPreview from "./CameraPreview";
import PhotoGallery from "./PhotoGallery";

interface ComplaintFormProps {
  language: Language;
}

const ComplaintForm = ({ language }: ComplaintFormProps) => {
  const { toast } = useToast();
  const t = translations[language];

  // Get GPS coordinates on component mount
  useEffect(() => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setLocation({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          });
        },
        (error) => {
          console.error('GPS access error:', error);
          toast({
            title: language === 'ar' ? "تحذير" : "Avertissement",
            description: language === 'ar' ? "لا يمكن الحصول على الموقع الجغرافي" : "Impossible d'obtenir la localisation GPS",
            variant: "default"
          });
        }
      );
    }
  }, []);
  
  const [sector, setSector] = useState("");
  const [photos, setPhotos] = useState<File[]>([]);
  const [mediaData, setMediaData] = useState<{
    video: File | null;
    audio: File | null;
    audioTranscription: string | null;
  }>({
    video: null,
    audio: null,
    audioTranscription: null
  });
  const [location, setLocation] = useState<{
    latitude: number | null;
    longitude: number | null;
  }>({
    latitude: null,
    longitude: null
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isRecordingAudio, setIsRecordingAudio] = useState(false);
  const [isPlayingAudio, setIsPlayingAudio] = useState(false);
  const [isCameraActive, setIsCameraActive] = useState(false);
  const [countdown, setCountdown] = useState(0);
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioRecorderRef = useRef<MediaRecorder | null>(null);
  const audioPlayerRef = useRef<HTMLAudioElement | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const sectors = [
    { value: "roads", label: t.sectors.roads, icon: "🛣️" },
    { value: "lighting", label: t.sectors.lighting, icon: "💡" },
    { value: "trash", label: t.sectors.trash, icon: "🗑️" },
    { value: "water", label: t.sectors.water, icon: "💧" },
    { value: "parks", label: t.sectors.parks, icon: "🌳" },
    { value: "safety", label: t.sectors.safety, icon: "🚨" },
    { value: "noise", label: t.sectors.noise, icon: "🔊" },
    { value: "other", label: t.sectors.other, icon: "📝" }
  ];

  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    
    if (photos.length + files.length > 5) {
      toast({
        title: language === 'ar' ? "تجاوز الحد الأقصى" : "Limite dépassée",
        description: language === 'ar' ? "يمكنك رفع 5 صور كحد أقصى" : "Vous pouvez télécharger maximum 5 photos",
        variant: "destructive"
      });
      return;
    }

    const validFiles = files.filter(file => {
      if (!file.type.startsWith('image/')) {
        toast({
          title: language === 'ar' ? "نوع ملف غير صحيح" : "Type de fichier invalide",
          description: language === 'ar' ? "يرجى اختيار صور فقط" : "Veuillez sélectionner des images uniquement",
          variant: "destructive"
        });
        return false;
      }
      return true;
    });

    setPhotos(prev => [...prev, ...validFiles]);
  };

  const removePhoto = (index: number) => {
    setPhotos(prev => prev.filter((_, i) => i !== index));
  };

  const takePhoto = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { 
          facingMode: 'environment', // Use back camera on mobile
          width: { ideal: 1920 },
          height: { ideal: 1080 }
        } 
      });
      
      streamRef.current = stream;
      setIsCameraActive(true);
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.style.display = 'block';
        
        // Wait for video to be ready
        videoRef.current.onloadedmetadata = () => {
          if (videoRef.current) {
            videoRef.current.play();
          }
        };
        
        setCountdown(0);
      }
    } catch (error) {
      console.error('Camera access error:', error);
      toast({
        title: language === 'ar' ? "خطأ" : "Erreur",
        description: language === 'ar' ? "لا يمكن الوصول للكاميرا. تأكد من السماح بالوصول للكاميرا" : "Impossible d'accéder à la caméra. Assurez-vous d'autoriser l'accès",
        variant: "destructive"
      });
    }
  };

  const capturePhoto = () => {
    if (videoRef.current && streamRef.current) {
      if (photos.length >= 5) {
        toast({
          title: language === 'ar' ? "تجاوز الحد الأقصى" : "Limite dépassée",
          description: language === 'ar' ? "يمكنك رفع 5 صور كحد أقصى" : "Vous pouvez télécharger maximum 5 photos",
          variant: "destructive"
        });
        stopCamera();
        return;
      }

      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      canvas.width = videoRef.current.videoWidth;
      canvas.height = videoRef.current.videoHeight;
      
      if (context) {
        context.drawImage(videoRef.current, 0, 0);
        canvas.toBlob((blob) => {
          if (blob) {
            const file = new File([blob], `complaint-photo-${Date.now()}.jpg`, { type: 'image/jpeg' });
            setPhotos(prev => [...prev, file]);
            toast({
              title: language === 'ar' ? "تم التقاط الصورة" : "Photo capturée",
              description: language === 'ar' ? "تم حفظ الصورة بنجاح" : "Photo sauvegardée avec succès",
              className: "border-green-200 bg-green-50 text-green-800"
            });
          }
        }, 'image/jpeg', 0.9);
      }
      
      stopCamera();
    }
  };

  const takeVideo = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
      streamRef.current = stream;
      setIsCameraActive(true);
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.style.display = 'block';
        videoRef.current.onloadedmetadata = () => {
          if (videoRef.current) {
            videoRef.current.play();
          }
        };
      }
      
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      const chunks: BlobPart[] = [];
      
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data);
        }
      };
      
      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: 'video/webm' });
        const file = new File([blob], `complaint-video-${Date.now()}.webm`, { type: 'video/webm' });
        setMediaData(prev => ({ ...prev, video: file }));
        stopCamera();
      };
      
      mediaRecorder.start();
      setIsRecording(true);
      
    } catch (error) {
      toast({
        title: language === 'ar' ? "خطأ" : "Erreur",
        description: language === 'ar' ? "لا يمكن الوصول للكاميرا/الميكروفون" : "Impossible d'accéder à la caméra/microphone",
        variant: "destructive"
      });
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const startAudioRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      audioRecorderRef.current = mediaRecorder;
      const chunks: BlobPart[] = [];
      
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data);
        }
      };
      
      mediaRecorder.onstop = async () => {
        const blob = new Blob(chunks, { type: 'audio/webm' });
        const file = new File([blob], `complaint-audio-${Date.now()}.webm`, { type: 'audio/webm' });
        setMediaData(prev => ({ ...prev, audio: file }));
        
        // Transcribe the audio
        await transcribeAudio(blob);
        
        stream.getTracks().forEach(track => track.stop());
      };
      
      mediaRecorder.start();
      setIsRecordingAudio(true);
      
    } catch (error) {
      toast({
        title: language === 'ar' ? "خطأ" : "Erreur", 
        description: language === 'ar' ? "لا يمكن الوصول للميكروفون" : "Impossible d'accéder au microphone",
        variant: "destructive"
      });
    }
  };

  const stopAudioRecording = () => {
    if (audioRecorderRef.current && isRecordingAudio) {
      audioRecorderRef.current.stop();
      setIsRecordingAudio(false);
    }
  };

  const transcribeAudio = async (audioBlob: Blob) => {
    try {
      // Convert audio blob to base64
      const arrayBuffer = await audioBlob.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      const base64Audio = btoa(String.fromCharCode.apply(null, Array.from(uint8Array)));

      // Call transcription function
      const { data, error } = await supabase.functions.invoke('transcribe-audio', {
        body: { audio: base64Audio }
      });

      if (error) {
        console.error('Transcription error:', error);
        return;
      }

      if (data.success && data.text) {
        setMediaData(prev => ({ ...prev, audioTranscription: data.text }));
        toast({
          title: language === 'ar' ? "تم نسخ الصوت" : "Audio transcrit",
          description: language === 'ar' ? "تم تحويل الصوت إلى نص بنجاح" : "Audio transcrit avec succès",
          className: "border-green-200 bg-green-50 text-green-800"
        });
      }
    } catch (error) {
      console.error('Error transcribing audio:', error);
    }
  };

  const playAudio = () => {
    if (mediaData.audio && !isPlayingAudio) {
      const audioUrl = URL.createObjectURL(mediaData.audio);
      audioPlayerRef.current = new Audio(audioUrl);
      audioPlayerRef.current.play();
      setIsPlayingAudio(true);
      
      audioPlayerRef.current.onended = () => {
        setIsPlayingAudio(false);
        URL.revokeObjectURL(audioUrl);
      };
    }
  };

  const stopAudio = () => {
    if (audioPlayerRef.current && isPlayingAudio) {
      audioPlayerRef.current.pause();
      audioPlayerRef.current.currentTime = 0;
      setIsPlayingAudio(false);
    }
  };

  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    if (videoRef.current) {
      videoRef.current.srcObject = null;
      videoRef.current.style.display = 'none';
    }
    setIsCameraActive(false);
    setCountdown(0);
    setIsRecording(false);
  };

  const handleSubmit = async () => {
    if (!sector) {
      toast({
        title: "Missing Information",
        description: language === 'ar' ? "يرجى اختيار القطاع" : "Veuillez sélectionner le secteur",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Create FormData to send files
      const formData = new FormData();
      formData.append('sector', sector);
      formData.append('language', language);
      
      // Add GPS coordinates if available
      if (location.latitude && location.longitude) {
        formData.append('latitude', location.latitude.toString());
        formData.append('longitude', location.longitude.toString());
      }
      
      // Add all photos
      photos.forEach((photo, index) => {
        formData.append(`photo_${index}`, photo);
      });
      
      if (mediaData.video) formData.append('video', mediaData.video);
      if (mediaData.audio) formData.append('audio', mediaData.audio);
      if (mediaData.audioTranscription) formData.append('audioTranscription', mediaData.audioTranscription);

      // Call Supabase Edge Function to create Jira issue
      const { data, error } = await supabase.functions.invoke('create-jira-complaint', {
        body: formData
      });

      if (error) throw error;
      
      toast({
        title: t.complaintsSubmitted,
        description: t.complaintsDescription,
        className: "border-green-200 bg-green-50 text-green-800"
      });

      // Reset form
      setSector("");
      setPhotos([]);
      setMediaData({ video: null, audio: null, audioTranscription: null });
      // Keep location for future complaints

    } catch (error) {
      toast({
        title: "Submission Failed",
        description: "There was an error submitting your complaint. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6" dir={language === 'ar' ? 'rtl' : 'ltr'}>
      <div className="text-center">
        <h2 className="text-3xl font-bold text-foreground mb-2">{t.reportIssue}</h2>
        <p className="text-muted-foreground">{t.complaintsDescription}</p>
      </div>

      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2" style={{ flexDirection: language === 'ar' ? 'row-reverse' : 'row' }}>
            <AlertTriangle className="w-5 h-5 text-orange-500" />
            <span>{t.submitComplaint}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Sector Selection */}
          <div className="space-y-2">
            <Label>{t.selectSector} *</Label>
            <Select value={sector} onValueChange={setSector}>
              <SelectTrigger>
                <SelectValue placeholder={t.selectSector} />
              </SelectTrigger>
              <SelectContent>
                {sectors.map((sectorItem) => (
                  <SelectItem key={sectorItem.value} value={sectorItem.value}>
                    <div className="flex items-center space-x-2" style={{ flexDirection: language === 'ar' ? 'row-reverse' : 'row' }}>
                      <span>{sectorItem.icon}</span>
                      <span>{sectorItem.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Enhanced Photo Upload Section */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 mb-4" style={{ flexDirection: language === 'ar' ? 'row-reverse' : 'row' }}>
              <ImageIcon className="w-5 h-5 text-primary" />
              <Label className="text-lg font-semibold">
                {language === 'ar' ? 'الصور (اختياري - حد أقصى 5 صور)' : 'Photos (optionnel - maximum 5 photos)'}
              </Label>
            </div>
            
            <CameraPreview
              ref={videoRef}
              isCameraActive={isCameraActive}
              countdown={countdown}
              language={language}
              onCapturePhoto={capturePhoto}
              onStopCamera={stopCamera}
              photosLength={photos.length}
            />
            
            {!isCameraActive && (
              <div className="grid gap-4 md:grid-cols-2">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={takePhoto}
                  className="h-20 hover-lift interactive-glow border-2 border-dashed border-primary/30 hover:border-primary/60 transition-all"
                  disabled={photos.length >= 5}
                >
                  <div className="flex flex-col items-center space-y-2">
                    <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                      <Camera className="w-6 h-6 text-primary" />
                    </div>
                    <span className="font-medium">{t.takePhoto}</span>
                  </div>
                </Button>
                
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => fileInputRef.current?.click()}
                  className="h-20 hover-lift interactive-glow border-2 border-dashed border-secondary/30 hover:border-secondary/60 transition-all"
                  disabled={photos.length >= 5}
                >
                  <div className="flex flex-col items-center space-y-2">
                    <div className="w-12 h-12 bg-secondary/10 rounded-xl flex items-center justify-center">
                      <Upload className="w-6 h-6 text-secondary-foreground" />
                    </div>
                    <span className="font-medium">{language === 'ar' ? 'رفع صور' : 'Télécharger'}</span>
                  </div>
                </Button>
              </div>
            )}

            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept="image/*"
              onChange={handlePhotoUpload}
              className="hidden"
            />

            {/* Enhanced Photo Gallery */}
            {photos.length > 0 && (
              <div className="space-y-4 animate-fade-in-up">
                <div className="flex items-center justify-between">
                  <Label className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>{language === 'ar' ? 'الصور المرفوعة:' : 'Photos téléchargées:'}</span>
                  </Label>
                  <div className="text-sm text-muted-foreground">
                    {photos.length}/5
                  </div>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {photos.map((photo, index) => (
                    <div key={index} className="relative group animate-scale-in" style={{ animationDelay: `${index * 0.1}s` }}>
                      <div className="gradient-card p-1 rounded-xl shadow-card">
                        <img
                          src={URL.createObjectURL(photo)}
                          alt={`Photo ${index + 1}`}
                          className="w-full h-32 object-cover rounded-lg"
                        />
                      </div>
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        className="absolute -top-2 -right-2 w-8 h-8 p-0 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-all transform scale-75 group-hover:scale-100"
                        onClick={() => removePhoto(index)}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                      <div className="absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
                        #{index + 1}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Enhanced Video Recording */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2 mb-2" style={{ flexDirection: language === 'ar' ? 'row-reverse' : 'row' }}>
                <Video className="w-5 h-5 text-primary" />
                <Label className="text-lg font-semibold">{language === 'ar' ? 'الفيديو (اختياري)' : 'Vidéo (optionnel)'}</Label>
              </div>
              <Button 
                type="button" 
                variant={isRecording ? "destructive" : "outline"} 
                onClick={isRecording ? stopRecording : takeVideo}
                className="w-full h-20 hover-lift interactive-glow transition-all"
              >
                <div className="flex flex-col items-center space-y-2">
                  <div className={`w-12 h-12 ${isRecording ? 'bg-destructive/20' : 'bg-primary/10'} rounded-xl flex items-center justify-center`}>
                    {isRecording ? (
                      <StopCircle className="w-6 h-6 text-destructive animate-pulse" />
                    ) : (
                      <Video className="w-6 h-6 text-primary" />
                    )}
                  </div>
                  <span className="font-medium">
                    {isRecording ? (language === 'ar' ? 'إيقاف التسجيل' : 'Arrêter l\'enregistrement') : t.takeVideo}
                  </span>
                </div>
              </Button>
              
              {mediaData.video && (
                <div className="animate-fade-in-up">
                  <div className="bg-green-50 border border-green-200 rounded-lg p-3 flex items-center space-x-2">
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <span className="text-green-700 font-medium">{t.videoRecorded}</span>
                  </div>
                </div>
              )}
            </div>

            {/* Enhanced Audio Recording */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2 mb-2" style={{ flexDirection: language === 'ar' ? 'row-reverse' : 'row' }}>
                <Mic className="w-5 h-5 text-primary" />
                <Label className="text-lg font-semibold">{language === 'ar' ? 'التسجيل الصوتي (اختياري)' : 'Enregistrement audio (optionnel)'}</Label>
              </div>
              <div className="flex gap-3">
                <Button 
                  type="button" 
                  variant={isRecordingAudio ? "destructive" : "outline"} 
                  onClick={isRecordingAudio ? stopAudioRecording : startAudioRecording}
                  className="flex-1 h-20 hover-lift interactive-glow transition-all"
                >
                  <div className="flex flex-col items-center space-y-2">
                    <div className={`w-12 h-12 ${isRecordingAudio ? 'bg-destructive/20' : 'bg-primary/10'} rounded-xl flex items-center justify-center`}>
                      {isRecordingAudio ? (
                        <StopCircle className="w-6 h-6 text-destructive animate-pulse" />
                      ) : (
                        <Mic className="w-6 h-6 text-primary" />
                      )}
                    </div>
                    <span className="font-medium text-sm">
                      {isRecordingAudio 
                        ? (language === 'ar' ? 'إيقاف التسجيل' : 'Arrêter') 
                        : (language === 'ar' ? 'بدء التسجيل' : 'Commencer')
                      }
                    </span>
                  </div>
                </Button>
                
                {mediaData.audio && (
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={isPlayingAudio ? stopAudio : playAudio}
                    className="h-20 px-6 hover-lift interactive-glow"
                  >
                    <div className="flex flex-col items-center space-y-2">
                      <div className="w-12 h-12 bg-accent/10 rounded-xl flex items-center justify-center">
                        {isPlayingAudio ? (
                          <Pause className="w-6 h-6 text-accent-foreground" />
                        ) : (
                          <Play className="w-6 h-6 text-accent-foreground" />
                        )}
                      </div>
                      <span className="text-xs font-medium">
                        {isPlayingAudio ? (language === 'ar' ? 'إيقاف' : 'Pause') : (language === 'ar' ? 'تشغيل' : 'Play')}
                      </span>
                    </div>
                  </Button>
                )}
              </div>
              
              {mediaData.audio && (
                <div className="animate-fade-in-up space-y-2">
                  <div className="bg-green-50 border border-green-200 rounded-lg p-3 flex items-center space-x-2">
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <span className="text-green-700 font-medium">
                      {language === 'ar' ? 'تم تسجيل الصوت' : 'Audio enregistré'}
                    </span>
                  </div>
                  
                  {mediaData.audioTranscription && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                      <div className="flex items-center space-x-2 mb-2">
                        <CheckCircle className="w-4 h-4 text-blue-500" />
                        <span className="text-blue-700 font-medium text-sm">
                          {language === 'ar' ? 'نص الصوت:' : 'Transcription:'}
                        </span>
                      </div>
                      <p className="text-sm text-blue-800 bg-white/50 rounded p-2">
                        {mediaData.audioTranscription}
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Enhanced Media Status Summary */}
            {(photos.length > 0 || mediaData.video || mediaData.audio) && (
              <div className="space-y-3 p-4 bg-muted/30 rounded-xl border animate-fade-in-up">
                <div className="flex items-center space-x-2 mb-2">
                  <Zap className="w-4 h-4 text-primary" />
                  <Label className="font-semibold text-sm">
                    {language === 'ar' ? 'ملخص المرفقات' : 'Résumé des pièces jointes'}
                  </Label>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  {photos.length > 0 && (
                    <div className="flex items-center space-x-2 text-sm">
                      <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <CheckCircle className="w-4 h-4 text-green-600" />
                      </div>
                      <span className="font-medium">
                        {photos.length} {language === 'ar' ? 'صور' : 'photos'}
                      </span>
                    </div>
                  )}
                  {mediaData.video && (
                    <div className="flex items-center space-x-2 text-sm">
                      <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <CheckCircle className="w-4 h-4 text-blue-600" />
                      </div>
                      <span className="font-medium">{t.videoRecorded}</span>
                    </div>
                  )}
                  {mediaData.audio && (
                    <div className="flex items-center space-x-2 text-sm">
                      <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                        <CheckCircle className="w-4 h-4 text-purple-600" />
                      </div>
                      <span className="font-medium">
                        {language === 'ar' ? 'تسجيل صوتي' : 'Audio enregistré'}
                        {mediaData.audioTranscription && (
                          <span className="text-xs text-muted-foreground ml-1">
                            ({language === 'ar' ? 'مُنسوخ' : 'transcrit'})
                          </span>
                        )}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Enhanced Submit Button */}
          <Button 
            onClick={handleSubmit} 
            disabled={!sector || isSubmitting}
            className="w-full h-16 bg-gradient-primary text-white font-semibold text-lg hover-lift interactive-glow disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <div className="flex items-center space-x-2">
                <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                <span>{language === 'ar' ? 'جاري الإرسال...' : 'Envoi en cours...'}</span>
              </div>
            ) : (
              <div className="flex items-center space-x-2" style={{ flexDirection: language === 'ar' ? 'row-reverse' : 'row' }}>
                <Send className="w-5 h-5" />
                <span>{t.submitComplaint}</span>
              </div>
            )}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default ComplaintForm;