import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import "https://deno.land/x/xhr@0.1.0/mod.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const { message, language } = await req.json()
    
    if (!message) {
      throw new Error('Message is required')
    }

    const openAIApiKey = Deno.env.get('OPENAI_API_KEY')
    if (!openAIApiKey) {
      throw new Error('OpenAI API key is not configured')
    }

    console.log(`AI Assistant request - Language: ${language}, Message: ${message}`)

    // Enhanced system prompt for multilingual support with complaint detection
    const systemPrompt = `You are an intelligent assistant for the city of Agadir, Morocco. You help citizens and tourists with information about:

1. Local news and events in Agadir
2. Tourist attractions and activities  
3. Municipal complaints and issues
4. General municipal services information

IMPORTANT LANGUAGE INSTRUCTIONS:
- If the user writes in Arabic, respond in Arabic
- If the user writes in French, respond in French  
- If the user writes in English, respond in English
- For any other language, respond in that same language
- If you cannot determine the language, respond in ${language === 'ar' ? 'Arabic' : 'French'}

COMPLAINT DETECTION:
If the user wants to make a complaint, submit a complaint, report an issue, or asks about complaint procedures, respond with exactly this JSON format:
{"action": "redirect_to_complaints", "message": "I'll redirect you to the complaints form where you can submit your issue with photos, videos, and audio recordings."}

For Arabic users wanting to complain, respond with:
{"action": "redirect_to_complaints", "message": "سأوجهك إلى نموذج الشكاوى حيث يمكنك تقديم مشكلتك مع الصور والفيديوهات والتسجيلات الصوتية."}

For French users wanting to complain, respond with:
{"action": "redirect_to_complaints", "message": "Je vais vous rediriger vers le formulaire de plaintes où vous pouvez soumettre votre problème avec des photos, vidéos et enregistrements audio."}

For all other requests, respond normally with helpful information.

Current user interface language preference: ${language === 'ar' ? 'Arabic' : 'French'}`

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4.1-2025-04-14',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: message }
        ],
        max_tokens: 500,
        temperature: 0.7
      }),
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('OpenAI API error:', errorText)
      throw new Error(`OpenAI API error: ${response.status}`)
    }

    const data = await response.json()
    const aiResponse = data.choices[0].message.content

    console.log(`AI Assistant response: ${aiResponse}`)

    // Check if the response contains a redirect action
    let responseData = { response: aiResponse, success: true };
    
    try {
      // Try to parse as JSON to check for actions
      const parsedResponse = JSON.parse(aiResponse);
      if (parsedResponse.action === 'redirect_to_complaints') {
        responseData = {
          response: parsedResponse.message,
          success: true,
          action: 'redirect_to_complaints'
        };
      }
    } catch (e) {
      // Not JSON, treat as regular response
    }

    return new Response(
      JSON.stringify(responseData),
      { 
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        } 
      }
    )

  } catch (error) {
    console.error('Error in AI assistant:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      { 
        status: 500,
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        } 
      }
    )
  }
})