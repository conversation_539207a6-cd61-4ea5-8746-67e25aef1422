import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft, MapPin, Navigation, Search, Star, ExternalLink } from "lucide-react";
import { translations, Language } from "@/utils/translations";
import LanguageSelector from "@/components/LanguageSelector";

const MapPage = () => {
  const [language, setLanguage] = useState<Language | null>(() => {
    const saved = localStorage.getItem('agadir-language');
    return saved as Language || null;
  });
  const navigate = useNavigate();

  // Save language to localStorage when it changes
  const handleLanguageSelect = (lang: Language) => {
    setLanguage(lang);
    localStorage.setItem('agadir-language', lang);
  };

  if (!language) {
    return <LanguageSelector onLanguageSelect={handleLanguageSelect} />;
  }

  const t = translations[language];

  const locations = [
    {
      id: 1,
      name: language === 'ar' ? "شاطئ أكادير" : "Plage d'Agadir",
      description: language === 'ar' 
        ? "شاطئ رملي رائع يمتد لـ 10 كم على طول المحيط الأطلسي"
        : "Magnifique plage de sable fin s'étendant sur 10 km le long de l'océan Atlantique",
      coordinates: [30.428, -9.640] as [number, number], // lat, lng for Google Maps
      color: "bg-blue-500"
    },
    {
      id: 2,
      name: language === 'ar' ? "سوق الأحد" : "Souk El Had",
      description: language === 'ar'
        ? "أكبر سوق تقليدي في أكادير مع أكثر من 3000 محل تجاري"
        : "Le plus grand marché traditionnel d'Agadir avec plus de 3000 boutiques",
      coordinates: [30.420, -9.598] as [number, number],
      color: "bg-green-500"
    },
    {
      id: 3,
      name: language === 'ar' ? "مارينا أكادير" : "Marina d'Agadir",
      description: language === 'ar'
        ? "ميناء ترفيهي حديث مع مطاعم ومقاهي ومحلات تجارية"
        : "Port de plaisance moderne avec restaurants, cafés et boutiques",
      coordinates: [30.420, -9.635] as [number, number],
      color: "bg-red-500"
    },
    {
      id: 4,
      name: language === 'ar' ? "قصبة أكادير أوفلا" : "Kasbah Agadir Oufella",
      description: language === 'ar'
        ? "موقع تاريخي على قمة التل يوفر إطلالة بانورامية رائعة"
        : "Site historique au sommet de la colline offrant une vue panoramique",
      coordinates: [30.427, -9.601] as [number, number],
      color: "bg-purple-500"
    }
  ];

  const openInGoogleMaps = (coordinates: [number, number], name: string) => {
    const [lat, lng] = coordinates;
    const url = `https://www.google.com/maps/search/?api=1&query=${lat},${lng}&query_place_id=${encodeURIComponent(name)}`;
    window.open(url, '_blank');
  };

  return (
    <div className="min-h-screen bg-background" dir={language === 'ar' ? 'rtl' : 'ltr'}>
      {/* Header */}
      <header className="bg-gradient-hero">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3" style={{ flexDirection: language === 'ar' ? 'row-reverse' : 'row' }}>
              <Button
                variant="ghost"
                onClick={() => navigate('/')}
                className="text-white hover:bg-white/10 rounded-full p-2"
              >
                <ArrowLeft className="w-5 h-5" style={{ transform: language === 'ar' ? 'scaleX(-1)' : 'none' }} />
              </Button>
              <div className="flex items-center space-x-2" style={{ flexDirection: language === 'ar' ? 'row-reverse' : 'row' }}>
                <MapPin className="w-6 h-6 text-white" />
                <h1 className="text-2xl font-serif font-bold text-white">
                  {language === 'ar' ? "خريطة أكادير" : "Carte d'Agadir"}
                </h1>
              </div>
            </div>
            <Button
              variant="ghost"
              onClick={() => navigate('/tourism')}
              className="text-white hover:bg-white/10 rounded-full px-4"
            >
              <Navigation className="w-4 h-4 mr-2" />
              <span>{language === 'ar' ? "السياحة" : "Tourisme"}</span>
            </Button>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-6">
        {/* Google Maps and Location List */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Google Maps Embed */}
          <div className="lg:col-span-2">
            <Card className="overflow-hidden shadow-elegant">
              <CardContent className="p-0">
                <div className="relative h-[600px]">
                  <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d108044.87!2d-9.640!3d30.428!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0xdb3b6f4d36b4873%3A0x8b5b9b9b9b9b9b9b!2sAgadir%2C%20Morocco!5e0!3m2!1sen!2sus!4v1635789123456!5m2!1sen!2sus"
                    width="100%"
                    height="100%"
                    style={{ border: 0 }}
                    allowFullScreen
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                    className="rounded-lg"
                  ></iframe>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Location List */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 mb-4">
              <Search className="w-5 h-5 text-muted-foreground" />
              <h2 className="text-xl font-serif font-semibold">
                {language === 'ar' ? "الأماكن السياحية" : "Lieux touristiques"}
              </h2>
            </div>

            {locations.map((location) => (
              <Card key={location.id} className="hover-lift cursor-pointer interactive-glow" onClick={() => openInGoogleMaps(location.coordinates, location.name)}>
                <CardContent className="p-4">
                  <div className="flex items-start space-x-3" style={{ flexDirection: language === 'ar' ? 'row-reverse' : 'row' }}>
                    <div className={`w-12 h-12 ${location.color} rounded-lg flex items-center justify-center flex-shrink-0`}>
                      <MapPin className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-sm leading-tight mb-1">
                        {location.name}
                      </h3>
                      <p className="text-xs text-muted-foreground leading-relaxed mb-2">
                        {location.description}
                      </p>
                      <Button 
                        variant="outline" 
                        size="sm"
                        className="h-7 px-3 text-xs hover-lift"
                        onClick={(e) => {
                          e.stopPropagation();
                          openInGoogleMaps(location.coordinates, location.name);
                        }}
                      >
                        <ExternalLink className="w-3 h-3 mr-1" />
                        {language === 'ar' ? "عرض في خرائط جوجل" : "Ouvrir dans Google Maps"}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Info Section */}
        <Card className="mt-6 gradient-card p-1 shadow-elegant">
          <CardContent className="bg-background rounded-lg p-6">
            <div className="text-center">
              <h3 className="text-xl font-serif font-semibold mb-2">
                {language === 'ar' ? "أكادير، المغرب" : "Agadir, Maroc"}
              </h3>
              <p className="text-muted-foreground mb-4">
                {language === 'ar' 
                  ? "اكتشف الأماكن الرائعة التي لا يمكن تفويتها في لؤلؤة جنوب المغرب"
                  : "Découvrez les lieux incontournables de la perle du Sud du Maroc"
                }
              </p>
              <div className="flex items-center justify-center space-x-4 text-sm text-muted-foreground">
                <div className="flex items-center">
                  <Star className="w-4 h-4 text-accent mr-1" />
                  <span>4.8/5</span>
                </div>
                <div className="flex items-center">
                  <MapPin className="w-4 h-4 mr-1" />
                  <span>{language === 'ar' ? "أكثر من 20 موقع" : "Plus de 20 sites"}</span>
      </div>
    </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default MapPage;