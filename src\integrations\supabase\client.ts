// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://jurhmsupygtgczrqthwg.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imp1cmhtc3VweWd0Z2N6cnF0aHdnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQwMDM2MTksImV4cCI6MjA2OTU3OTYxOX0.YCzaOHSwcwBd9CLG5Qsy9iVBCkAQ8JUUGdALjydLig0";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});