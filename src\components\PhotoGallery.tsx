import { But<PERSON> } from "@/components/ui/button";
import { X } from "lucide-react";
import { Language } from "@/utils/translations";

interface PhotoGalleryProps {
  photos: File[];
  language: Language;
  onRemovePhoto: (index: number) => void;
}

const PhotoGallery = ({ photos, language, onRemovePhoto }: PhotoGalleryProps) => {
  if (photos.length === 0) return null;

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-4">
      {photos.map((photo, index) => (
        <div key={index} className="relative group">
          <img
            src={URL.createObjectURL(photo)}
            alt={`Photo ${index + 1}`}
            className="w-full h-24 object-cover rounded-lg border-2 border-primary/20 group-hover:border-primary/40 transition-all"
          />
          <Button
            type="button"
            variant="destructive"
            size="sm"
            onClick={() => onRemovePhoto(index)}
            className="absolute -top-2 -right-2 w-6 h-6 rounded-full p-0 opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <X className="w-3 h-3" />
          </Button>
          <div className="absolute bottom-1 left-1 bg-black/70 text-white text-xs px-2 py-1 rounded">
            {language === 'ar' ? `صورة ${index + 1}` : `Photo ${index + 1}`}
          </div>
        </div>
      ))}
    </div>
  );
};

export default PhotoGallery;