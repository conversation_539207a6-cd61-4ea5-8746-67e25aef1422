import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, Users } from "lucide-react";

const NewsSection = () => {
  const newsItems = [
    {
      id: 1,
      title: "New Beach Promenade Extension Completed",
      description: "The municipality of Agadir announces the completion of the extended beach promenade with new lighting and seating areas.",
      category: "Community",
      date: "2024-01-28",
      time: "10:00 AM",
      image: "https://images.unsplash.com/photo-1500375592092-40eb2168fd21?auto=format&fit=crop&w=800&q=80"
    },
    {
      id: 2,
      title: "Avenue Hassan II Renovation Update",
      description: "Major road improvements on Avenue Hassan II will continue through February. Traffic diversions are in place.",
      category: "Infrastructure",
      date: "2024-01-27",
      time: "2:30 PM",
      image: "https://images.unsplash.com/photo-1473091534298-04dcbce3278c?auto=format&fit=crop&w=800&q=80"
    },
    {
      id: 3,
      title: "Free Health Screening at Kasbah",
      description: "Ministry of Health organizes free medical consultations and vaccinations at the historic Kasbah this weekend.",
      category: "Health",
      date: "2024-01-26",
      time: "9:00 AM",
      image: "https://images.unsplash.com/photo-1466442929976-97f336a657be?auto=format&fit=crop&w=800&q=80"
    },
    {
      id: 4,
      title: "Water Distribution Maintenance Notice",
      description: "Temporary water service interruption in Hay Mohammadi district on Sunday from 6 AM to 12 PM.",
      category: "Utilities",
      date: "2024-01-25",
      time: "8:00 AM",
      image: "https://images.unsplash.com/photo-1482938289607-d4a9ddbe4151?auto=format&fit=crop&w=800&q=80"
    }
  ];

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "Community": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "Infrastructure": return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "Health": return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      case "Utilities": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-foreground mb-2">Latest News & Updates</h2>
        <p className="text-muted-foreground">Stay informed about what's happening in your community</p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {newsItems.map((item) => (
          <Card key={item.id} className="hover:shadow-lg transition-shadow cursor-pointer">
            <div className="relative">
              <img
                src={item.image}
                alt={item.title}
                className="w-full h-48 object-cover rounded-t-lg"
              />
              <Badge className={`absolute top-3 left-3 ${getCategoryColor(item.category)}`}>
                {item.category}
              </Badge>
            </div>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg leading-tight">{item.title}</CardTitle>
              <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                <div className="flex items-center space-x-1">
                  <Calendar className="w-4 h-4" />
                  <span>{item.date}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>{item.time}</span>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-base">{item.description}</CardDescription>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default NewsSection;