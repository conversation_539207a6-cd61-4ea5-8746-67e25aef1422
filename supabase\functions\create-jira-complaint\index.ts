import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import "https://deno.land/x/xhr@0.1.0/mod.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    console.log('Creating Jira complaint...')
    
    const formData = await req.formData()
    const sector = formData.get('sector') as string
    const language = formData.get('language') as string
    const latitude = formData.get('latitude') as string | null
    const longitude = formData.get('longitude') as string | null
    const audioTranscription = formData.get('audioTranscription') as string | null
    
    // Handle multiple photos
    const photos: File[] = []
    for (let i = 0; i < 5; i++) {
      const photo = formData.get(`photo_${i}`) as File | null
      if (photo) photos.push(photo)
    }
    
    const video = formData.get('video') as File | null
    const audio = formData.get('audio') as File | null

    if (!sector) {
      throw new Error('Sector is required')
    }

    console.log(`Processing complaint for sector: ${sector}, language: ${language}`)
    console.log(`GPS coordinates: ${latitude && longitude ? `${latitude}, ${longitude}` : 'Not available'}`)
    console.log(`Audio transcription: ${audioTranscription ? 'Available' : 'Not available'}`)
    console.log(`Media attached - Photos: ${photos.length}, Video: ${video ? 'Yes' : 'No'}, Audio: ${audio ? 'Yes' : 'No'}`)

    // Get Jira credentials from environment
    const jiraUrl = Deno.env.get('JIRA_URL')
    const jiraEmail = Deno.env.get('JIRA_EMAIL')  
    const jiraApiToken = Deno.env.get('JIRA_API_TOKEN')
    const jiraProjectKey = Deno.env.get('JIRA_PROJECT_KEY')

    console.log('Jira configuration check:')
    console.log(`URL: ${jiraUrl ? 'Set' : 'Missing'}`)
    console.log(`Email: ${jiraEmail ? 'Set' : 'Missing'}`)
    console.log(`Token: ${jiraApiToken ? 'Set' : 'Missing'}`)
    console.log(`Project: ${jiraProjectKey ? 'Set' : 'Missing'}`)

    if (!jiraUrl || !jiraEmail || !jiraApiToken || !jiraProjectKey) {
      throw new Error('Jira configuration is missing. Please check your environment variables.')
    }

    // Clean up Jira URL (remove trailing slash)
    const cleanJiraUrl = jiraUrl.replace(/\/$/, '')
    console.log(`Using Jira URL: ${cleanJiraUrl}`)

    // Create simplified issue description for better compatibility
    const locationInfo = latitude && longitude ? `\nGPS Coordinates: ${latitude}, ${longitude}\nGoogle Maps: https://www.google.com/maps?q=${latitude},${longitude}` : '\nGPS Coordinates: Not available'
    const transcriptionInfo = audioTranscription ? `\nAudio Transcription: ${audioTranscription}` : ''
    const description = `Sector: ${sector}\nLanguage: ${language}${locationInfo}${transcriptionInfo}\nMedia: ${photos.length} photos, ${video ? '1 video' : 'no video'}, ${audio ? '1 audio' : 'no audio'}\nSubmitted via Agadir CivicHub`

    // Simplified issue data for better compatibility
    const issueData = {
      fields: {
        project: {
          key: jiraProjectKey
        },
        summary: `Agadir Complaint: ${sector} (${language === 'ar' ? 'العربية' : 'Français'})`,
        description: description,
        issuetype: {
          id: "10007"
        }
      }
    }

    console.log('Creating Jira issue with data:', JSON.stringify(issueData, null, 2))

    // Create the Jira issue
    const jiraResponse = await fetch(`${cleanJiraUrl}/rest/api/2/issue`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${btoa(`${jiraEmail}:${jiraApiToken}`)}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(issueData)
    })

    console.log(`Jira API response status: ${jiraResponse.status}`)

    if (!jiraResponse.ok) {
      const errorText = await jiraResponse.text()
      console.error('Jira API error response:', errorText)
      
      // Try to parse the error for more details
      let errorDetails = errorText
      try {
        const errorJson = JSON.parse(errorText)
        errorDetails = errorJson.errors ? JSON.stringify(errorJson.errors) : errorText
      } catch (e) {
        // Keep original error text if parsing fails
      }
      
      throw new Error(`Jira API error (${jiraResponse.status}): ${errorDetails}`)
    }

    const jiraResult = await jiraResponse.json()
    const issueKey = jiraResult.key

    console.log(`Created Jira issue: ${issueKey}`)

    // Upload attachments to Jira if media files exist
    const attachmentResults = []
    
    // Upload photos
    for (let i = 0; i < photos.length; i++) {
      try {
        const photo = photos[i]
        const attachmentFormData = new FormData()
        attachmentFormData.append('file', photo)
        
        const attachmentResponse = await fetch(`${cleanJiraUrl}/rest/api/2/issue/${issueKey}/attachments`, {
          method: 'POST',
          headers: {
            'Authorization': `Basic ${btoa(`${jiraEmail}:${jiraApiToken}`)}`,
            'X-Atlassian-Token': 'no-check'
          },
          body: attachmentFormData
        })
        
        if (attachmentResponse.ok) {
          console.log(`Successfully uploaded photo ${i + 1}`)
          attachmentResults.push(`Photo ${i + 1}: uploaded`)
        } else {
          console.error(`Failed to upload photo ${i + 1}:`, await attachmentResponse.text())
          attachmentResults.push(`Photo ${i + 1}: failed`)
        }
      } catch (error) {
        console.error(`Error uploading photo ${i + 1}:`, error)
        attachmentResults.push(`Photo ${i + 1}: error`)
      }
    }
    
    // Upload video if exists
    if (video) {
      try {
        const attachmentFormData = new FormData()
        attachmentFormData.append('file', video)
        
        const attachmentResponse = await fetch(`${cleanJiraUrl}/rest/api/2/issue/${issueKey}/attachments`, {
          method: 'POST',
          headers: {
            'Authorization': `Basic ${btoa(`${jiraEmail}:${jiraApiToken}`)}`,
            'X-Atlassian-Token': 'no-check'
          },
          body: attachmentFormData
        })
        
        if (attachmentResponse.ok) {
          console.log('Successfully uploaded video')
          attachmentResults.push('Video: uploaded')
        } else {
          console.error('Failed to upload video:', await attachmentResponse.text())
          attachmentResults.push('Video: failed')
        }
      } catch (error) {
        console.error('Error uploading video:', error)
        attachmentResults.push('Video: error')
      }
    }
    
    // Upload audio if exists
    if (audio) {
      try {
        const attachmentFormData = new FormData()
        attachmentFormData.append('file', audio)
        
        const attachmentResponse = await fetch(`${cleanJiraUrl}/rest/api/2/issue/${issueKey}/attachments`, {
          method: 'POST',
          headers: {
            'Authorization': `Basic ${btoa(`${jiraEmail}:${jiraApiToken}`)}`,
            'X-Atlassian-Token': 'no-check'
          },
          body: attachmentFormData
        })
        
        if (attachmentResponse.ok) {
          console.log('Successfully uploaded audio')
          attachmentResults.push('Audio: uploaded')
        } else {
          console.error('Failed to upload audio:', await attachmentResponse.text())
          attachmentResults.push('Audio: failed')
        }
      } catch (error) {
        console.error('Error uploading audio:', error)
        attachmentResults.push('Audio: error')
      }
    }
    
    console.log('Attachment upload results:', attachmentResults)

    return new Response(
      JSON.stringify({ 
        success: true, 
        issueKey,
        message: `Complaint submitted successfully. Issue ${issueKey} created.`
      }),
      { 
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        } 
      }
    )

  } catch (error) {
    console.error('Error creating Jira complaint:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      { 
        status: 500,
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        } 
      }
    )
  }
})