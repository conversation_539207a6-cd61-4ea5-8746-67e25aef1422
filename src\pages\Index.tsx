import { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { MapPin, Calendar, Users, AlertTriangle, Camera, Bot, Navigation, Waves, ShoppingBag, Trash2, Bus, Trees, Droplets, Zap, Construction, Volume2, Shield, Lightbulb, Car, Building } from "lucide-react";
import NewsSection from "@/components/NewsSection";
import TouristAttractions from "@/components/TouristAttractions";
import ComplaintForm from "@/components/ComplaintForm";
import LanguageSelector from "@/components/LanguageSelector";
import AIAssistant from "@/components/AIAssistant";
import { translations, Language } from "@/utils/translations";
import agadirHero from "@/assets/agadir-hero.jpg";

const Index = () => {
  const [searchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState("home");
  const [language, setLanguage] = useState<Language | null>(() => {
    const saved = localStorage.getItem('agadir-language');
    return saved as Language || null;
  });
  const [showAssistant, setShowAssistant] = useState(false);
  const navigate = useNavigate();

  // Handle URL parameters for tab switching and assistant
  useEffect(() => {
    const tab = searchParams.get('tab');
    const assistant = searchParams.get('assistant');
    
    if (tab && ['home', 'news', 'complaints'].includes(tab)) {
      setActiveTab(tab);
      setShowAssistant(false);
    }
    
    if (assistant === 'true') {
      setShowAssistant(true);
    }
  }, [searchParams]);

  // Save language to localStorage when it changes
  const handleLanguageSelect = (lang: Language) => {
    setLanguage(lang);
    localStorage.setItem('agadir-language', lang);
  };

  // Handle tab changes and update URL
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    if (tab === 'home') {
      navigate('/', { replace: true });
    } else {
      navigate(`/?tab=${tab}`, { replace: true });
    }
  };

  // Show language selector first
  if (!language) {
    return <LanguageSelector onLanguageSelect={handleLanguageSelect} />;
  }

  // Show AI assistant when requested
  if (showAssistant) {
    return (
      <AIAssistant 
        language={language} 
        onNavigate={(tab) => {
          if (tab === "tourism") {
            navigate("/tourism");
          } else if (tab === "map") {
            navigate("/map");
          } else {
            setActiveTab(tab);
            setShowAssistant(false);
          }
        }}
        onBack={() => setShowAssistant(false)}
      />
    );
  }

  const t = translations[language];

  // Complaint categories based on your reference image
  const complaintCategories = [
    {
      title: language === 'ar' ? "أسواق الأطعمة" : "Marchés",
      subtitle: language === 'ar' ? "بائع أو سلعة ؟" : "Vendeur ou marchandise ?",
      icon: ShoppingBag,
      color: "bg-blue-500",
      type: "markets"
    },
    {
      title: language === 'ar' ? "قمامة" : "Déchets",
      subtitle: language === 'ar' ? "النفايات أو الحاويات ؟" : "Déchets ou contenants ?",
      icon: Trash2,
      color: "bg-green-500",
      type: "waste"
    },
    {
      title: language === 'ar' ? "المواصلات" : "Transport",
      subtitle: language === 'ar' ? "محطة الحافلة أو حافلة ؟" : "Arrêt de bus ou bus ?",
      icon: Bus,
      color: "bg-red-500", 
      type: "transport"
    },
    {
      title: language === 'ar' ? "المساحات الخضراء" : "Espaces verts",
      subtitle: language === 'ar' ? "حدائق ؟" : "Jardins ?",
      icon: Trees,
      color: "bg-green-600",
      type: "green_spaces"
    },
    {
      title: language === 'ar' ? "المياه والصرف" : "Eau et Assainissement",
      subtitle: language === 'ar' ? "تسرب أو انقطاع ؟" : "Fuite ou coupure ?",
      icon: Droplets,
      color: "bg-blue-600",
      type: "water"
    },
    {
      title: language === 'ar' ? "الكهرباء" : "Électricité",
      subtitle: language === 'ar' ? "انقطاع أو عطل ؟" : "Panne ou coupure ?",
      icon: Zap,
      color: "bg-yellow-500",
      type: "electricity"
    },
    {
      title: language === 'ar' ? "الطرق والبنية التحتية" : "Routes et Infrastructure",
      subtitle: language === 'ar' ? "حفر أو أضرار ؟" : "Nids de poule ou dégâts ?",
      icon: Construction,
      color: "bg-orange-500",
      type: "roads"
    },
    {
      title: language === 'ar' ? "الضوضاء" : "Nuisances sonores",
      subtitle: language === 'ar' ? "إزعاج أو صوت عالي ؟" : "Gêne ou bruit fort ?",
      icon: Volume2,
      color: "bg-purple-500",
      type: "noise"
    },
    {
      title: language === 'ar' ? "الأمن العام" : "Sécurité publique",
      subtitle: language === 'ar' ? "خطر أو حادث ؟" : "Danger ou incident ?",
      icon: Shield,
      color: "bg-red-600",
      type: "security"
    },
    {
      title: language === 'ar' ? "إنارة الشوارع" : "Éclairage public",
      subtitle: language === 'ar' ? "مصباح معطل ؟" : "Lampadaire défaillant ?",
      icon: Lightbulb,
      color: "bg-amber-500",
      type: "lighting"
    },
    {
      title: language === 'ar' ? "الشاطئ والساحل" : "Plage et Littoral",
      subtitle: language === 'ar' ? "تلوث أو تآكل ؟" : "Pollution ou érosion ?",
      icon: Waves,
      color: "bg-cyan-500",
      type: "beach"
    },
    {
      title: language === 'ar' ? "مواقف السيارات" : "Stationnement",
      subtitle: language === 'ar' ? "انتهاك أو مشكلة ؟" : "Infraction ou problème ?",
      icon: Car,
      color: "bg-gray-500",
      type: "parking"
    },
    {
      title: language === 'ar' ? "المباني والتشييد" : "Bâtiments et Construction",
      subtitle: language === 'ar' ? "مخالفة أو خطر ؟" : "Infraction ou danger ?",
      icon: Building,
      color: "bg-indigo-500",
      type: "buildings"
    }
  ];

  return (
    <div className="min-h-screen bg-background pattern-dots" dir={language === 'ar' ? 'rtl' : 'ltr'}>
      {/* Hero Header with Beautiful Agadir Image */}
      <header className="relative h-96 overflow-hidden">
        <div className="absolute inset-0">
          <img 
            src={agadirHero} 
            alt="Agadir Panoramic View" 
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent" />
        </div>
        
        <div className="relative h-full flex flex-col">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center space-x-3 animate-fade-in-up" style={{ flexDirection: language === 'ar' ? 'row-reverse' : 'row' }}>
              <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-glow">
                <Users className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-serif font-bold text-white">{t.appName}</h1>
                <p className="text-white/80 text-sm font-medium">{t.location}</p>
              </div>
            </div>
            <Button
              variant="secondary"
              size="sm"
              onClick={() => setShowAssistant(true)}
              className="bg-white/10 border-white/20 text-white hover:bg-white/20 backdrop-blur-sm interactive-glow animate-glow"
            >
              <Bot className="w-4 h-4 mr-2" />
              <span>{language === 'ar' ? "المساعد الذكي" : "Assistant IA"}</span>
            </Button>
          </div>
          
          <div className="flex-1 flex items-end p-6">
            <div className="text-white animate-fade-in-up" style={{ animationDelay: '0.3s' }}>
              <h2 className="text-5xl font-serif font-bold mb-4">
                {language === 'ar' ? "أهلاً بكم في أكادير" : "Bienvenue à Agadir"}
              </h2>
              <p className="text-xl text-white/90 mb-6">
                {language === 'ar' ? "لؤلؤة جنوب المغرب" : "La perle du Sud du Maroc"}
              </p>
              <div className="flex space-x-4" style={{ flexDirection: language === 'ar' ? 'row-reverse' : 'row' }}>
                <Button 
                  onClick={() => navigate('/tourism')}
                  className="bg-gradient-primary text-white hover-lift interactive-glow"
                >
                  <Camera className="w-4 h-4 mr-2" />
                  {language === 'ar' ? "اكتشف السياحة" : "Découvrir le tourisme"}
                </Button>
                <Button 
                  variant="outline"
                  onClick={() => navigate('/map')}
                  className="bg-gradient-secondary border-white/30 text-white hover:bg-white/10 backdrop-blur-sm"
                >
                  <MapPin className="w-4 h-4 mr-2" />
                  {language === 'ar' ? "عرض الخريطة" : "Voir la carte"}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Bottom Navigation - Now handled by BottomNavigation component */}
      
      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        {activeTab === "home" && (
          <div className="space-y-8 animate-fade-in-up">
            {/* Quick Actions */}
            <div>
              <h3 className="text-2xl font-serif font-bold mb-6 text-center">
                {language === 'ar' ? "خدمات المدينة" : "Services de la ville"}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="gradient-card p-1 shadow-elegant hover-lift interactive-glow cursor-pointer" onClick={() => navigate('/tourism')}>
                  <CardContent className="bg-background rounded-lg p-6">
                    <div className="flex items-center space-x-4" style={{ flexDirection: language === 'ar' ? 'row-reverse' : 'row' }}>
                      <div className="w-16 h-16 bg-blue-500 rounded-xl flex items-center justify-center">
                        <Waves className="w-8 h-8 text-white" />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-lg font-serif font-semibold mb-2">
                          {language === 'ar' ? "الشواطئ والرياضات المائية" : "Plages et Sports Nautiques"}
                        </h4>
                        <p className="text-sm text-muted-foreground">
                          {language === 'ar' 
                            ? "أكادير مشهورة بـ 300 يوم من أشعة الشمس سنوياً وشاطئها الرائع"
                            : "Agadir est réputée pour ses 300 jours de soleil par an et sa magnifique plage"
                          }
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="gradient-card p-1 shadow-elegant hover-lift interactive-glow cursor-pointer" onClick={() => navigate('/tourism')}>
                  <CardContent className="bg-background rounded-lg p-6">
                    <div className="flex items-center space-x-4" style={{ flexDirection: language === 'ar' ? 'row-reverse' : 'row' }}>
                      <div className="w-16 h-16 bg-purple-500 rounded-xl flex items-center justify-center">
                        <Camera className="w-8 h-8 text-white" />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-lg font-serif font-semibold mb-2">
                          {language === 'ar' ? "الثقافة والتاريخ" : "Culture et Histoire"}
                        </h4>
                        <p className="text-sm text-muted-foreground">
                          {language === 'ar'
                            ? "اكتشف التراث الأمازيغي الغني وتاريخ المدينة العريق"
                            : "Découvrez le riche patrimoine berbère et l'histoire fascinante de la ville"
                          }
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Complaint Categories */}
            <div>
              <h3 className="text-2xl font-serif font-bold mb-2 text-center">
                {language === 'ar' ? "الإبلاغ عن مشكلة" : "Signaler un problème"}
              </h3>
              <p className="text-muted-foreground text-center mb-6">
                {language === 'ar' ? "اختر نوع المشكلة التي تريد الإبلاغ عنها" : "Choisissez le type de problème à signaler"}
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {complaintCategories.map((category, index) => (
                  <Card key={index} className="hover-lift cursor-pointer interactive-glow animate-fade-in-up" style={{ animationDelay: `${0.1 + index * 0.05}s` }} onClick={() => handleTabChange("complaints")}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className={`w-14 h-14 ${category.color} rounded-xl flex items-center justify-center`}>
                          <category.icon className="w-7 h-7 text-white" />
                        </div>
                        <Button variant="outline" size="sm" className="hover-lift text-xs">
                          {language === 'ar' ? "إبلاغ" : "Signaler"}
                        </Button>
                      </div>
                      <h4 className="text-base font-semibold mb-1 leading-tight">{category.title}</h4>
                      <p className="text-xs text-muted-foreground">{category.subtitle}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === "news" && (
          <div className="animate-fade-in-up">
            <NewsSection />
          </div>
        )}

        {activeTab === "complaints" && (
          <div className="animate-fade-in-up">
            <ComplaintForm language={language} />
          </div>
        )}
      </div>
    </div>
  );
};

export default Index;