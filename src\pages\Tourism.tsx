import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Star, Waves, ShoppingBag, Utensils, Camera, MapPin, Clock, Users } from "lucide-react";
import { translations, Language } from "@/utils/translations";
import LanguageSelector from "@/components/LanguageSelector";
import agadirHero from "@/assets/agadir-hero.jpg";

const Tourism = () => {
  const [language, setLanguage] = useState<Language | null>(() => {
    const saved = localStorage.getItem('agadir-language');
    return saved as Language || null;
  });
  const navigate = useNavigate();

  // Save language to localStorage when it changes
  const handleLanguageSelect = (lang: Language) => {
    setLanguage(lang);
    localStorage.setItem('agadir-language', lang);
  };

  if (!language) {
    return <LanguageSelector onLanguageSelect={handleLanguageSelect} />;
  }

  const t = translations[language];

  const attractions = [
    {
      id: 1,
      name: language === 'ar' ? "شاطئ أكادير" : "Plage d'Agadir",
      description: language === 'ar' 
        ? "شاطئ رملي رائع يمتد لـ 10 كم على طول المحيط الأطلسي. مثالي للسباحة وركوب الأمواج والرياضات المائية."
        : "Magnifique plage de sable fin s'étendant sur 10 km le long de l'océan Atlantique. Parfait pour la natation et les sports nautiques.",
      icon: Waves,
      color: "bg-blue-500",
      highlights: language === 'ar' 
        ? ["ركوب الأمواج طوال السنة", "10 كم من الشاطئ الرملي", "مدارس الرياضات المائية"]
        : ["Surf et kitesurf toute l'année", "10 km de plage de sable fin", "Écoles de sports nautiques"]
    },
    {
      id: 2,
      name: language === 'ar' ? "سوق الأحد" : "Souk El Had",
      description: language === 'ar'
        ? "أكبر سوق تقليدي في أكادير مع أكثر من 3000 محل تجاري. اكتشف الحرف اليدوية والتوابل والمنتجات المحلية."
        : "Le plus grand marché traditionnel d'Agadir avec plus de 3000 boutiques. Découvrez l'artisanat et les produits locaux.",
      icon: ShoppingBag,
      color: "bg-green-500",
      highlights: language === 'ar'
        ? ["أكثر من 3000 محل", "الحرف التقليدية", "التوابل والأعشاب"]
        : ["Plus de 3000 boutiques", "Artisanat traditionnel", "Épices et herbes"]
    },
    {
      id: 3,
      name: language === 'ar' ? "مارينا أكادير" : "Marina d'Agadir",
      description: language === 'ar'
        ? "ميناء ترفيهي حديث مع مطاعم ومقاهي ومحلات تجارية. مكان مثالي للتنزه والاستمتاع بالإطلالة على البحر."
        : "Port de plaisance moderne avec restaurants, cafés et boutiques. Parfait pour une promenade avec vue sur mer.",
      icon: Utensils,
      color: "bg-red-500",
      highlights: language === 'ar'
        ? ["مطاعم فاخرة", "إطلالة على البحر", "تسوق راقي"]
        : ["Restaurants raffinés", "Vue sur mer", "Shopping de luxe"]
    },
    {
      id: 4,
      name: language === 'ar' ? "قصبة أكادير أوفلا" : "Kasbah Agadir Oufella",
      description: language === 'ar'
        ? "موقع تاريخي على قمة التل يوفر إطلالة بانورامية رائعة على المدينة والساحل. شاهد عبارة 'الله الوطن الملك' المضيئة."
        : "Site historique au sommet de la colline offrant une vue panoramique sur la ville et la côte. Admirez l'inscription illuminée.",
      icon: Camera,
      color: "bg-purple-500",
      highlights: language === 'ar'
        ? ["إطلالة بانورامية", "موقع تاريخي", "مناظر غروب الشمس"]
        : ["Vue panoramique", "Site historique", "Couchers de soleil"]
    }
  ];

  const categories = [
    {
      title: language === 'ar' ? "الشواطئ والرياضات المائية" : "Plages et Sports Nautiques",
      icon: Waves,
      description: language === 'ar' 
        ? "أكادير مشهورة بـ 300 يوم من أشعة الشمس سنوياً وشاطئها الرائع الذي يمتد لـ 10 كم."
        : "Agadir est réputée pour ses 300 jours de soleil par an et sa magnifique plage de 10 km.",
    },
    {
      title: language === 'ar' ? "الثقافة والتاريخ" : "Culture et Histoire", 
      icon: Camera,
      description: language === 'ar'
        ? "اكتشف التراث الأمازيغي الغني وتاريخ المدينة العريق."
        : "Découvrez le riche patrimoine berbère et l'histoire fascinante de la ville.",
    }
  ];

  return (
    <div className="min-h-screen bg-background pattern-dots" dir={language === 'ar' ? 'rtl' : 'ltr'}>
      {/* Hero Header */}
      <header className="relative h-80 overflow-hidden">
        <div className="absolute inset-0">
          <img 
            src={agadirHero} 
            alt="Agadir Panoramic View" 
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
        </div>
        
        <div className="relative h-full flex flex-col">
          <div className="flex items-center justify-between p-4">
            <Button
              variant="ghost"
              onClick={() => navigate('/')}
              className="text-white hover:bg-white/10 rounded-full p-2"
            >
              <ArrowLeft className="w-5 h-5" style={{ transform: language === 'ar' ? 'scaleX(-1)' : 'none' }} />
            </Button>
            <Button
              variant="ghost"
              onClick={() => navigate('/map')}
              className="text-white hover:bg-white/10 rounded-full px-4"
            >
              <MapPin className="w-4 h-4 mr-2" />
              <span>{language === 'ar' ? "الخريطة" : "Carte"}</span>
            </Button>
          </div>
          
          <div className="flex-1 flex items-end p-6">
            <div className="text-white animate-fade-in-up">
              <h1 className="text-4xl font-serif font-bold mb-2">
                {language === 'ar' ? "اكتشف أكادير" : "Découvrez Agadir"}
              </h1>
              <p className="text-xl text-white/90 mb-4">
                {language === 'ar' ? "لؤلؤة جنوب المغرب" : "La perle du Sud du Maroc"}
              </p>
              <div className="flex items-center text-white/80">
                <MapPin className="w-4 h-4 mr-2" />
                <span>{language === 'ar' ? "أكادير، المغرب" : "Agadir, Maroc"}</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Categories Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
          {categories.map((category, index) => (
            <Card key={index} className="gradient-card p-1 shadow-elegant hover-lift">
              <div className="bg-background rounded-lg p-6">
                <div className="flex items-center space-x-3 mb-4" style={{ flexDirection: language === 'ar' ? 'row-reverse' : 'row' }}>
                  <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                    <category.icon className="w-6 h-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-serif font-semibold">{category.title}</h3>
                </div>
                <p className="text-muted-foreground leading-relaxed">{category.description}</p>
              </div>
            </Card>
          ))}
        </div>

        {/* Tourist Attractions */}
        <div className="animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
          <h2 className="text-3xl font-serif font-bold mb-6 text-center">
            {language === 'ar' ? "الأماكن السياحية" : "Lieux touristiques"}
          </h2>
          
          <div className="space-y-6">
            {attractions.map((attraction, index) => (
              <Card key={attraction.id} className="overflow-hidden shadow-card hover-lift interactive-glow">
                <CardContent className="p-0">
                  <div className="flex flex-col md:flex-row">
                    <div className={`w-full md:w-48 h-48 md:h-auto ${attraction.color} flex items-center justify-center relative overflow-hidden`}>
                      <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent" />
                      <attraction.icon className="w-16 h-16 text-white relative z-10" />
                      <div className="absolute top-4 right-4">
                        <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
                          <Star className="w-3 h-3 mr-1" />
                          4.8
                        </Badge>
      </div>
    </div>
                    
                    <div className="flex-1 p-6">
                      <div className="flex justify-between items-start mb-3">
                        <h3 className="text-2xl font-serif font-bold">{attraction.name}</h3>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => {
                            const [lat, lng] = [30.425, -9.615]; // Agadir center coordinates
                            const url = `https://www.google.com/maps/search/?api=1&query=${lat},${lng}&query_place_id=${encodeURIComponent(attraction.name)}`;
                            window.open(url, '_blank');
                          }}
                          className="ml-4 hover-lift"
                        >
                          <MapPin className="w-4 h-4 mr-2" />
                          {language === 'ar' ? "عرض في خرائط جوجل" : "Ouvrir dans Google Maps"}
                        </Button>
                      </div>
                      
                      <p className="text-muted-foreground mb-4 leading-relaxed">
                        {attraction.description}
                      </p>
                      
                      <div className="space-y-2">
                        {attraction.highlights.map((highlight, idx) => (
                          <div key={idx} className="flex items-center text-sm">
                            <Star className="w-4 h-4 text-accent mr-2 flex-shrink-0" />
                            <span>{highlight}</span>
                          </div>
                        ))}
                      </div>
                      
                      <div className="flex items-center justify-between mt-4 pt-4 border-t border-border">
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Clock className="w-4 h-4 mr-2" />
                          <span>{language === 'ar' ? "مفتوح يومياً" : "Ouvert tous les jours"}</span>
                        </div>
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Users className="w-4 h-4 mr-2" />
                          <span>{language === 'ar' ? "مناسب للعائلات" : "Adapté aux familles"}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Tourism;