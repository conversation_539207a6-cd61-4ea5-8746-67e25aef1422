import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Send, Bot, User, ArrowLeft, MessageCircle, Info, Camera, Mic, MicOff, Upload, Volume2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { translations, Language } from "@/utils/translations";

interface Message {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
  isAudio?: boolean;
}

interface AIAssistantProps {
  language: Language;
  onNavigate: (tab: string) => void;
  onBack: () => void;
}

const AIAssistant = ({ language, onNavigate, onBack }: AIAssistantProps) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isTranscribing, setIsTranscribing] = useState(false);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const t = translations[language];

  // Initial welcome message
  useEffect(() => {
    const welcomeMessage: Message = {
      id: "welcome",
      content: language === 'ar' 
        ? "مرحباً! أنا مساعدك الذكي لمدينة أكادير. يمكنني فهم أي لغة تكتب بها أو تتحدث بها. اطرح أسئلتك حول الأخبار المحلية، الأماكن السياحية، أو الشكاوى. كيف يمكنني مساعدتك اليوم؟"
        : "Bonjour ! Je suis votre assistant intelligent pour la ville d'Agadir. Je peux comprendre toute langue que vous écrivez ou parlez. Posez vos questions sur les actualités locales, les attractions touristiques, ou les plaintes. Comment puis-je vous aider aujourd'hui ?",
      isUser: false,
      timestamp: new Date()
    };
    setMessages([welcomeMessage]);
  }, [language]);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  // Audio transcription function
  const transcribeAudio = async (audioBlob: Blob): Promise<string> => {
    const reader = new FileReader();
    return new Promise((resolve, reject) => {
      reader.onload = async () => {
        try {
          const arrayBuffer = reader.result as ArrayBuffer;
          const base64Audio = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
          
          const { data, error } = await supabase.functions.invoke('transcribe-audio', {
            body: { audio: base64Audio }
          });

          if (error) throw error;
          resolve(data.text || '');
        } catch (error) {
          reject(error);
        }
      };
      reader.onerror = reject;
      reader.readAsArrayBuffer(audioBlob);
    });
  };

  // Start audio recording
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          sampleRate: 24000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });
      
      audioChunksRef.current = [];
      mediaRecorderRef.current = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      
      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };
      
      mediaRecorderRef.current.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        await processAudioMessage(audioBlob);
        
        // Stop the stream
        stream.getTracks().forEach(track => track.stop());
      };
      
      mediaRecorderRef.current.start();
      setIsRecording(true);
      
      toast({
        title: language === 'ar' ? "بدأ التسجيل" : "Enregistrement démarré",
        description: language === 'ar' ? "اضغط مرة أخرى لإيقاف التسجيل" : "Cliquez à nouveau pour arrêter l'enregistrement",
      });
      
    } catch (error) {
      console.error('Error starting recording:', error);
      toast({
        title: language === 'ar' ? "خطأ" : "Erreur",
        description: language === 'ar' 
          ? "لا يمكن الوصول إلى الميكروفون"
          : "Impossible d'accéder au microphone",
        variant: "destructive",
      });
    }
  };

  // Stop audio recording
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  // Process audio message
  const processAudioMessage = async (audioBlob: Blob) => {
    setIsTranscribing(true);
    
    try {
      const transcribedText = await transcribeAudio(audioBlob);
      
      if (transcribedText.trim()) {
        const audioMessage: Message = {
          id: Date.now().toString(),
          content: transcribedText,
          isUser: true,
          timestamp: new Date(),
          isAudio: true
        };
        
        setMessages(prev => [...prev, audioMessage]);
        await sendMessageToAI(transcribedText);
      } else {
        toast({
          title: language === 'ar' ? "لم يتم فهم الصوت" : "Audio non compris",
          description: language === 'ar' 
            ? "لم أتمكن من فهم التسجيل الصوتي. حاول مرة أخرى."
            : "Je n'ai pas pu comprendre l'enregistrement audio. Veuillez réessayer.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error transcribing audio:', error);
      toast({
        title: language === 'ar' ? "خطأ في التحويل" : "Erreur de transcription",
        description: language === 'ar' 
          ? "حدث خطأ أثناء تحويل الصوت إلى نص"
          : "Une erreur s'est produite lors de la transcription audio",
        variant: "destructive",
      });
    } finally {
      setIsTranscribing(false);
    }
  };

  // Handle file upload
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('audio/')) {
      toast({
        title: language === 'ar' ? "نوع ملف غير صحيح" : "Type de fichier incorrect",
        description: language === 'ar' 
          ? "يرجى تحديد ملف صوتي فقط"
          : "Veuillez sélectionner un fichier audio uniquement",
        variant: "destructive",
      });
      return;
    }

    await processAudioMessage(file);
    event.target.value = ''; // Reset file input
  };

  // Send message to AI
  const sendMessageToAI = async (messageText: string) => {
    setIsLoading(true);

    try {
      const { data, error } = await supabase.functions.invoke('ai-assistant', {
        body: {
          message: messageText,
          language: language
        }
      });

      if (error) throw error;

      // Check if AI wants to redirect to complaints
      if (data.action === 'redirect_to_complaints') {
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: data.response,
          isUser: false,
          timestamp: new Date()
        };

        setMessages(prev => [...prev, aiMessage]);
        
        // Show a brief delay before redirecting
        setTimeout(() => {
          onNavigate("complaints");
        }, 1500);
        
        return;
      }

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: data.response,
        isUser: false,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, aiMessage]);

    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: language === 'ar' ? "خطأ" : "Erreur",
        description: language === 'ar' 
          ? "حدث خطأ أثناء إرسال الرسالة. حاول مرة أخرى."
          : "Une erreur s'est produite lors de l'envoi du message. Veuillez réessayer.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Send text message
  const sendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputMessage,
      isUser: true,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    const messageText = inputMessage;
    setInputMessage("");
    
    await sendMessageToAI(messageText);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  // Toggle recording
  const toggleRecording = () => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };

  const quickActions = [
    {
      label: language === 'ar' ? "الأخبار المحلية" : "Actualités locales",
      icon: Info,
      action: () => onNavigate("news")
    },
    {
      label: language === 'ar' ? "الأماكن السياحية" : "Attractions touristiques", 
      icon: Camera,
      action: () => onNavigate("attractions")
    },
    {
      label: language === 'ar' ? "تقديم شكوى" : "Soumettre une plainte",
      icon: MessageCircle,
      action: () => onNavigate("complaints")
    }
  ];

  return (
    <div className="min-h-screen bg-background pattern-dots" dir={language === 'ar' ? 'rtl' : 'ltr'}>
      {/* Modern Header with Gradient */}
      <header className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-hero opacity-95" />
        <div className="relative border-b border-white/20 backdrop-blur-sm">
          <div className="container mx-auto px-4 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3 animate-fade-in-up" style={{ flexDirection: language === 'ar' ? 'row-reverse' : 'row' }}>
                <Button
                  variant="ghost"
                  onClick={onBack}
                  className="p-2 text-white hover:bg-white/10 rounded-full"
                >
                  <ArrowLeft className="w-5 h-5" style={{ transform: language === 'ar' ? 'scaleX(-1)' : 'none' }} />
                </Button>
                <div className="flex items-center space-x-3" style={{ flexDirection: language === 'ar' ? 'row-reverse' : 'row' }}>
                  <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-glow animate-glow">
                    <Bot className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-serif font-bold text-white">
                      {language === 'ar' ? "المساعد الذكي" : "Assistant IA"}
                    </h1>
                    <p className="text-white/80 text-sm font-medium">
                      {language === 'ar' ? "لمدينة أكادير" : "Pour la ville d'Agadir"}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>


      {/* Chat Interface */}
      <Card className="mx-4 mb-4 flex flex-col" style={{ height: 'calc(100vh - 200px)' }}>
        <CardHeader className="pb-4 flex-shrink-0">
          <CardTitle className="flex items-center space-x-2" style={{ flexDirection: language === 'ar' ? 'row-reverse' : 'row' }}>
            <Bot className="w-5 h-5" />
            <span>{language === 'ar' ? "محادثة مع المساعد" : "Chat avec l'assistant"}</span>
          </CardTitle>
          <CardDescription>
            {language === 'ar' 
              ? "اطرح أسئلتك حول أكادير أو احصل على مساعدة"
              : "Posez vos questions sur Agadir ou obtenez de l'aide"
            }
          </CardDescription>
        </CardHeader>
        
        <CardContent className="flex-1 flex flex-col min-h-0">
          {/* Messages */}
          <ScrollArea className="flex-1 mb-4 min-h-0" ref={scrollAreaRef}>
            <div className="space-y-4 pr-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.isUser ? (language === 'ar' ? 'justify-start' : 'justify-end') : (language === 'ar' ? 'justify-end' : 'justify-start')}`}
                >
                  <div className={`flex items-start space-x-2 max-w-[80%] ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      message.isUser 
                        ? 'bg-primary text-primary-foreground' 
                        : 'bg-secondary text-secondary-foreground'
                    }`}>
                      {message.isUser ? (
                        message.isAudio ? <Volume2 className="w-4 h-4" /> : <User className="w-4 h-4" />
                      ) : (
                        <Bot className="w-4 h-4" />
                      )}
                    </div>
                    <div className={`rounded-lg p-3 ${
                      message.isUser
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-secondary text-secondary-foreground'
                    }`}>
                      <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                      {message.isAudio && (
                        <Badge variant="outline" className="mt-2 text-xs">
                          {language === 'ar' ? "رسالة صوتية" : "Message audio"}
                        </Badge>
                      )}
                      <p className="text-xs opacity-70 mt-1">
                        {message.timestamp.toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>

          {/* Input */}
          <div className="space-y-2">
            {/* Audio controls */}
            <div className="flex items-center justify-center space-x-2" style={{ flexDirection: language === 'ar' ? 'row-reverse' : 'row' }}>
              <Button
                variant={isRecording ? "destructive" : "outline"}
                size="sm"
                onClick={toggleRecording}
                disabled={isLoading || isTranscribing}
                className="flex items-center space-x-1"
                style={{ flexDirection: language === 'ar' ? 'row-reverse' : 'row' }}
              >
                {isRecording ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
                <span>
                  {isRecording 
                    ? (language === 'ar' ? "إيقاف التسجيل" : "Arrêter")
                    : (language === 'ar' ? "تسجيل صوتي" : "Enregistrer")
                  }
                </span>
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => fileInputRef.current?.click()}
                disabled={isLoading || isTranscribing}
                className="flex items-center space-x-1"
                style={{ flexDirection: language === 'ar' ? 'row-reverse' : 'row' }}
              >
                <Upload className="w-4 h-4" />
                <span>{language === 'ar' ? "رفع ملف صوتي" : "Charger audio"}</span>
              </Button>
              
              <input
                ref={fileInputRef}
                type="file"
                accept="audio/*"
                onChange={handleFileUpload}
                className="hidden"
              />
            </div>

            {isTranscribing && (
              <p className="text-sm text-muted-foreground text-center">
                {language === 'ar' ? "جاري تحويل الصوت إلى نص..." : "Transcription en cours..."}
              </p>
            )}

            {/* Text input */}
            <div className="flex space-x-2" style={{ flexDirection: language === 'ar' ? 'row-reverse' : 'row' }}>
              <div className="flex-1">
                <Input
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyDown={handleKeyPress}
                  placeholder={language === 'ar' ? "اكتب رسالتك بأي لغة..." : "Tapez votre message dans n'importe quelle langue..."}
                  disabled={isLoading || isTranscribing}
                />
              </div>
              <Button
                onClick={sendMessage}
                disabled={!inputMessage.trim() || isLoading || isTranscribing}
                size="icon"
              >
                <Send className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AIAssistant;