import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { MapPin, Star, Clock, Camera } from "lucide-react";

const TouristAttractions = () => {
  const attractions = [
    {
      id: 1,
      name: "Agadir Beach",
      description: "Stunning 10km stretch of golden sand beach with palm trees, perfect for swimming, surfing, and relaxation.",
      category: "Nature",
      rating: 4.8,
      reviews: 1324,
      image: "https://images.unsplash.com/photo-1500375592092-40eb2168fd21?auto=format&fit=crop&w=800&q=80",
      location: "Agadir Bay",
      openHours: "24/7",
      price: "Free"
    },
    {
      id: 2,
      name: "Agadir Kasbah",
      description: "Historic fortress ruins overlooking the city with panoramic views of the Atlantic Ocean and Atlas Mountains.",
      category: "Historical",
      rating: 4.6,
      reviews: 892,
      image: "https://images.unsplash.com/photo-1466442929976-97f336a657be?auto=format&fit=crop&w=800&q=80",
      location: "Hilltop - Old Agadir",
      openHours: "9:00 AM - 6:00 PM",
      price: "10 MAD Adults, 5 MAD Students"
    },
    {
      id: 3,
      name: "Souk El Had",
      description: "Morocco's largest souk with over 6,000 shops selling traditional crafts, spices, argan oil, and local products.",
      category: "Shopping",
      rating: 4.7,
      reviews: 756,
      image: "https://images.unsplash.com/photo-1482938289607-d4a9ddbe4151?auto=format&fit=crop&w=800&q=80",
      location: "City Center",
      openHours: "Daily: 8:00 AM - 1:00 PM, 3:00 PM - 7:00 PM",
      price: "Free Entry"
    },
    {
      id: 4,
      name: "Atlas Mountains Viewpoint",
      description: "Breathtaking panoramic views of the High Atlas Mountains and Agadir valley. Popular for hiking and photography.",
      category: "Nature",
      rating: 4.9,
      reviews: 567,
      image: "https://images.unsplash.com/photo-1469474968028-56623f02e42b?auto=format&fit=crop&w=800&q=80",
      location: "Atlas Foothills",
      openHours: "24/7",
      price: "Free"
    },
    {
      id: 5,
      name: "Marina Agadir",
      description: "Modern marina with luxury yachts, restaurants, cafes, and shopping. Perfect for evening strolls.",
      category: "Entertainment",
      rating: 4.5,
      reviews: 433,
      image: "https://images.unsplash.com/photo-1470813740244-df37b8c1edcb?auto=format&fit=crop&w=800&q=80",
      location: "Agadir Marina",
      openHours: "Daily: 10:00 AM - 11:00 PM",
      price: "Free Entry"
    },
    {
      id: 6,
      name: "La Médina d'Agadir",
      description: "Reconstructed traditional Berber village showcasing authentic Moroccan architecture, crafts, and culture.",
      category: "Culture",
      rating: 4.4,
      reviews: 298,
      image: "https://images.unsplash.com/photo-1487058792275-0ad4aaf24ca7?auto=format&fit=crop&w=800&q=80",
      location: "Ben Sergao",
      openHours: "Daily: 9:00 AM - 6:00 PM",
      price: "70 MAD Adults, 35 MAD Children"
    }
  ];

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "Historical": return "bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300";
      case "Nature": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "Culture": return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300";
      case "Shopping": return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "Entertainment": return "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-foreground mb-2">Discover Local Attractions</h2>
        <p className="text-muted-foreground">Explore the best places to visit in your city</p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {attractions.map((attraction) => (
          <Card key={attraction.id} className="hover:shadow-lg transition-shadow">
            <div className="relative">
              <img
                src={attraction.image}
                alt={attraction.name}
                className="w-full h-48 object-cover rounded-t-lg"
              />
              <Badge className={`absolute top-3 left-3 ${getCategoryColor(attraction.category)}`}>
                {attraction.category}
              </Badge>
              <div className="absolute top-3 right-3 bg-black/70 text-white px-2 py-1 rounded-md flex items-center space-x-1 text-sm">
                <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                <span>{attraction.rating}</span>
                <span className="text-gray-300">({attraction.reviews})</span>
              </div>
            </div>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg leading-tight">{attraction.name}</CardTitle>
              <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                <MapPin className="w-4 h-4" />
                <span>{attraction.location}</span>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <CardDescription className="text-sm">{attraction.description}</CardDescription>
              
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4 text-muted-foreground" />
                  <span className="text-muted-foreground">{attraction.openHours}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-foreground">Price: {attraction.price}</span>
                </div>
              </div>

              <div className="flex space-x-2 pt-2">
                <Button size="sm" className="flex-1">
                  <MapPin className="w-4 h-4 mr-1" />
                  View Map
                </Button>
                <Button size="sm" variant="outline" className="flex-1">
                  <Camera className="w-4 h-4 mr-1" />
                  Photos
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default TouristAttractions;