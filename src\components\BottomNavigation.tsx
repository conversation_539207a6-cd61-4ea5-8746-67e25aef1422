import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Users, Calendar, MapPin, Navigation, Bot } from "lucide-react";
import { translations, Language } from "@/utils/translations";

interface BottomNavigationProps {
  children: React.ReactNode;
}

const BottomNavigation = ({ children }: BottomNavigationProps) => {
  const [language, setLanguage] = useState<Language | null>(() => {
    const saved = localStorage.getItem('agadir-language');
    return saved as Language || null;
  });
  const navigate = useNavigate();
  const location = useLocation();

  const t = translations[language || 'fr'];

  const getActiveTab = () => {
    const path = location.pathname;
    if (path === '/') return 'home';
    if (path === '/tourism') return 'tourism';
    if (path === '/map') return 'map';
    return 'home';
  };

  const activeTab = getActiveTab();

  if (!language) {
    return <>{children}</>;
  }

  return (
    <div className="relative min-h-screen">
      {/* Main content with bottom padding */}
      <div className="pb-20">
        {children}
      </div>

      {/* Fixed Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-background/95 backdrop-blur-sm border-t border-border z-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-4 py-2">
            <Button
              variant={activeTab === "home" ? "default" : "ghost"}
              onClick={() => navigate('/')}
              className="flex flex-col items-center space-y-1 h-auto py-2"
            >
              <Users className="w-5 h-5" />
              <span className="text-xs font-medium">
                {language === 'ar' ? "الرئيسية" : "Accueil"}
              </span>
            </Button>
            <Button
              variant="ghost"
              onClick={() => navigate('/?tab=news')}
              className="flex flex-col items-center space-y-1 h-auto py-2"
            >
              <Calendar className="w-5 h-5" />
              <span className="text-xs font-medium">
                {language === 'ar' ? "الأخبار" : "Actualité"}
              </span>
            </Button>
            <Button
              variant={activeTab === "map" ? "default" : "ghost"}
              onClick={() => navigate('/map')}
              className="flex flex-col items-center space-y-1 h-auto py-2"
            >
              <MapPin className="w-5 h-5" />
              <span className="text-xs font-medium">
                {language === 'ar' ? "الخريطة" : "Carte"}
              </span>
            </Button>
            <Button
              variant={activeTab === "tourism" ? "default" : "ghost"}
              onClick={() => navigate('/tourism')}
              className="flex flex-col items-center space-y-1 h-auto py-2"
            >
              <Navigation className="w-5 h-5" />
              <span className="text-xs font-medium">
                {language === 'ar' ? "السياحة" : "Tourisme"}
              </span>
            </Button>
          </div>
        </div>
      </div>

      {/* Floating AI Assistant Button */}
      <div className="fixed bottom-24 right-6 z-50 animate-float">
        <Button
          size="lg"
          onClick={() => navigate('/?assistant=true')}
          className="bg-gradient-primary text-white shadow-glow hover:shadow-elegant rounded-full w-14 h-14 p-0 animate-glow"
        >
          <Bot className="w-6 h-6" />
        </Button>
      </div>
    </div>
  );
};

export default BottomNavigation;